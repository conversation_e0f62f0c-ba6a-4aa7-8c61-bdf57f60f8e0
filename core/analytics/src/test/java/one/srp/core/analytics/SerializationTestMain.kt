package one.srp.core.analytics

import kotlinx.serialization.json.Json
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventName
import one.srp.core.analytics.types.EventRefer

/**
 * 简单的测试主函数，用于验证序列化是否正常工作
 */
fun main() {
    val json = Json { 
        ignoreUnknownKeys = true
        prettyPrint = true
    }

    val event = MetricEvent(
        eventName = EventName.SelectItem,
        refer = EventRefer.Home,
        itemListName = EventItemListName.App,
        items = listOf(
            EventItem(
                itemCategory = EventItemCategory.GeneralCollage,
                itemId = "test_id",
                itemName = "test_name",
                index = 0
            )
        ),
        method = EventMethod.Click,
        actionType = EventActionType.Save,
        platform = "android",
        version = "1.0.0"
    )

    val jsonString = json.encodeToString(MetricEvent.serializer(), event)
    println("Serialized JSON:")
    println(jsonString)

    // 验证序列化结果包含 value 而不是枚举名称
    val checks = mapOf(
        "select_item" to "SelectItem",
        "home" to "Home", 
        "ap_app" to "App",
        "general_collage" to "GeneralCollage",
        "click" to "Click",
        "save" to "Save"
    )

    println("\n验证结果:")
    checks.forEach { (value, enumName) ->
        val hasValue = jsonString.contains("\"$value\"")
        val hasEnumName = jsonString.contains("\"$enumName\"")
        println("✓ 包含 '$value': $hasValue")
        println("✓ 不包含 '$enumName': ${!hasEnumName}")
        if (!hasValue || hasEnumName) {
            println("❌ 序列化验证失败!")
            return
        }
    }
    
    println("\n✅ 所有验证通过! 枚举序列化使用 value 属性成功!")
    
    // 测试反序列化
    println("\n测试反序列化:")
    try {
        val deserializedEvent = json.decodeFromString(MetricEvent.serializer(), jsonString)
        println("✅ 反序列化成功!")
        println("EventName: ${deserializedEvent.eventName} (value: ${deserializedEvent.eventName.value})")
        println("EventRefer: ${deserializedEvent.refer} (value: ${deserializedEvent.refer.value})")
    } catch (e: Exception) {
        println("❌ 反序列化失败: ${e.message}")
    }
}
