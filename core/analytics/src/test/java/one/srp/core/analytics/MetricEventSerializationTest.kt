package one.srp.core.analytics

import kotlinx.serialization.json.Json
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventName
import one.srp.core.analytics.types.EventRefer
import org.junit.Test
import org.junit.Assert.*

class MetricEventSerializationTest {

    private val json = Json { 
        ignoreUnknownKeys = true
        prettyPrint = true
    }

    @Test
    fun testMetricEventSerialization() {
        val event = MetricEvent(
            eventName = EventName.SelectItem,
            refer = EventRefer.Home,
            itemListName = EventItemListName.App,
            items = listOf(
                EventItem(
                    itemCategory = EventItemCategory.GeneralCollage,
                    itemId = "test_id",
                    itemName = "test_name",
                    index = 0
                )
            ),
            method = EventMethod.Click,
            actionType = EventActionType.Save,
            platform = "android",
            version = "1.0.0"
        )

        val jsonString = json.encodeToString(MetricEvent.serializer(), event)
        println("Serialized JSON:")
        println(jsonString)

        // 验证序列化结果包含 value 而不是枚举名称
        assertTrue("Should contain 'select_item' not 'SelectItem'", jsonString.contains("\"select_item\""))
        assertTrue("Should contain 'home' not 'Home'", jsonString.contains("\"home\""))
        assertTrue("Should contain 'ap_app' not 'App'", jsonString.contains("\"ap_app\""))
        assertTrue("Should contain 'general_collage' not 'GeneralCollage'", jsonString.contains("\"general_collage\""))
        assertTrue("Should contain 'click' not 'Click'", jsonString.contains("\"click\""))
        assertTrue("Should contain 'save' not 'Save'", jsonString.contains("\"save\""))

        // 验证不包含枚举名称
        assertFalse("Should not contain enum name 'SelectItem'", jsonString.contains("\"SelectItem\""))
        assertFalse("Should not contain enum name 'Home'", jsonString.contains("\"Home\""))
        assertFalse("Should not contain enum name 'App'", jsonString.contains("\"App\""))
        assertFalse("Should not contain enum name 'GeneralCollage'", jsonString.contains("\"GeneralCollage\""))
        assertFalse("Should not contain enum name 'Click'", jsonString.contains("\"Click\""))
        assertFalse("Should not contain enum name 'Save'", jsonString.contains("\"Save\""))
    }

    @Test
    fun testMetricEventDeserialization() {
        val jsonString = """
        {
            "eventName": "view_item",
            "refer": "profile",
            "itemListName": "ap_screen",
            "items": [
                {
                    "itemCategory": "product",
                    "itemId": "test_id",
                    "itemName": "test_name",
                    "index": 1
                }
            ],
            "method": "swipe",
            "actionType": "download",
            "platform": "android",
            "version": "1.0.0",
            "eventVersion": "1.0",
            "gensmoTimestamp": "1234567890000",
            "abInfo": "",
            "gensmoUserId": "",
            "gensmoUserType": "",
            "gensmoActiveId": "",
            "gensmoUserSourceId": "",
            "appsflyerId": ""
        }
        """.trimIndent()

        val event = json.decodeFromString(MetricEvent.serializer(), jsonString)

        assertEquals(EventName.ViewItem, event.eventName)
        assertEquals(EventRefer.Profile, event.refer)
        assertEquals(EventItemListName.Screen, event.itemListName)
        assertEquals(1, event.items.size)
        assertEquals(EventItemCategory.Product, event.items[0].itemCategory)
        assertEquals(EventMethod.Swipe, event.method)
        assertEquals(EventActionType.Download, event.actionType)
    }
}
