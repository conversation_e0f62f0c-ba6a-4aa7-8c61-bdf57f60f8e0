package one.srp.core.analytics.events

import kotlinx.serialization.Serializable
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventName
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.types.METRIC_VERSION


@Serializable
data class MetricEvent(
    val eventName: EventName,
    val refer: EventRefer,

    val itemListName: EventItemListName,
    val items: List<EventItem> = emptyList(),

    val method: EventMethod? = null,
    val actionType: EventActionType? = null,

    val platform: String = "",
    val version: String = "",
    val eventVersion: String = METRIC_VERSION,

    val gensmoTimestamp: String = (System.currentTimeMillis() * 1000).toString(),
    val abInfo: String = "",
    val gensmoUserId: String = "",
    val gensmoUserType: String = "",
    val gensmoActiveId: String = "",
    val gensmoUserSourceId: String = "",
    val appsflyerId: String = "",
)

@Serializable
sealed class MetricItem(
    open val itemListName: EventItemListName,
    open val items: List<EventItem> = emptyList(),

    open val method: EventMethod? = null,
    open val actionType: EventActionType? = null,

    open val eventName: EventName,
    open val refer: EventRefer = EventRefer.Default,
) {
    abstract fun copyWith(
        itemListName: EventItemListName = this.itemListName,
        items: List<EventItem> = this.items,
        method: EventMethod? = this.method,
        actionType: EventActionType? = this.actionType,
        eventName: EventName = this.eventName,
        refer: EventRefer = this.refer,
    ): MetricItem
}

data class SelectItem(
    override val itemListName: EventItemListName,
    override val items: List<EventItem> = emptyList(),

    override val method: EventMethod? = null,
    override val actionType: EventActionType? = null,

    override val eventName: EventName = EventName.SelectItem,
    override val refer: EventRefer = EventRefer.Default,
) : MetricItem(itemListName, items, method, actionType, eventName, refer) {
    override fun copyWith(
        itemListName: EventItemListName,
        items: List<EventItem>,
        method: EventMethod?,
        actionType: EventActionType?,
        eventName: EventName,
        refer: EventRefer,
    ): MetricItem = copy(
        itemListName = itemListName,
        items = items,
        method = method,
        actionType = actionType,
        eventName = eventName,
        refer = refer
    )
}

data class ViewItem(
    override val itemListName: EventItemListName,
    override val items: List<EventItem>,

    override val eventName: EventName = EventName.ViewItem,
    override val refer: EventRefer = EventRefer.Default,
) : MetricItem(itemListName, items, eventName = eventName, refer = refer) {
    override fun copyWith(
        itemListName: EventItemListName,
        items: List<EventItem>,
        method: EventMethod?,
        actionType: EventActionType?,
        eventName: EventName,
        refer: EventRefer,
    ): MetricItem = copy(
        itemListName = itemListName,
        items = items,
        eventName = eventName,
        refer = refer
    )
}


data class ViewItemList(
    override val itemListName: EventItemListName,
    override val items: List<EventItem>,

    override val eventName: EventName = EventName.ViewItemList,
    override val refer: EventRefer = EventRefer.Default,
) : MetricItem(itemListName, items, eventName = eventName, refer = refer) {
    override fun copyWith(
        itemListName: EventItemListName,
        items: List<EventItem>,
        method: EventMethod?,
        actionType: EventActionType?,
        eventName: EventName,
        refer: EventRefer,
    ): MetricItem = copy(
        itemListName = itemListName,
        items = items,
        eventName = eventName,
        refer = refer
    )
}


