package one.srp.core.analytics.serializers

import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventName
import one.srp.core.analytics.types.EventRefer

/**
 * 通用的枚举序列化器基类，用于序列化枚举的 value 属性
 */
abstract class EnumValueSerializer<T : Enum<T>>(
    private val enumClass: Class<T>,
    private val serialName: String,
    private val valueExtractor: (T) -> String,
    private val valueFinder: (String) -> T?
) : KSerializer<T> {
    
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(serialName, PrimitiveKind.STRING)
    
    override fun serialize(encoder: Encoder, value: T) {
        encoder.encodeString(valueExtractor(value))
    }
    
    override fun deserialize(decoder: Decoder): T {
        val value = decoder.decodeString()
        return valueFinder(value) ?: throw IllegalArgumentException("Unknown $serialName value: $value")
    }
}

/**
 * EventName 序列化器
 */
object EventNameSerializer : EnumValueSerializer<EventName>(
    enumClass = EventName::class.java,
    serialName = "EventName",
    valueExtractor = { it.value },
    valueFinder = { value -> EventName.values().find { it.value == value } }
)

/**
 * EventRefer 序列化器
 */
object EventReferSerializer : EnumValueSerializer<EventRefer>(
    enumClass = EventRefer::class.java,
    serialName = "EventRefer",
    valueExtractor = { it.value },
    valueFinder = { value -> EventRefer.values().find { it.value == value } }
)

/**
 * EventItemListName 序列化器
 */
object EventItemListNameSerializer : EnumValueSerializer<EventItemListName>(
    enumClass = EventItemListName::class.java,
    serialName = "EventItemListName",
    valueExtractor = { it.value },
    valueFinder = { value -> EventItemListName.values().find { it.value == value } }
)

/**
 * EventMethod 序列化器
 */
object EventMethodSerializer : EnumValueSerializer<EventMethod>(
    enumClass = EventMethod::class.java,
    serialName = "EventMethod",
    valueExtractor = { it.value },
    valueFinder = { value -> EventMethod.values().find { it.value == value } }
)

/**
 * EventActionType 序列化器
 */
object EventActionTypeSerializer : EnumValueSerializer<EventActionType>(
    enumClass = EventActionType::class.java,
    serialName = "EventActionType",
    valueExtractor = { it.value },
    valueFinder = { value -> EventActionType.values().find { it.value == value } }
)

/**
 * EventItemCategory 序列化器
 */
object EventItemCategorySerializer : EnumValueSerializer<EventItemCategory>(
    enumClass = EventItemCategory::class.java,
    serialName = "EventItemCategory",
    valueExtractor = { it.value },
    valueFinder = { value -> EventItemCategory.values().find { it.value == value } }
)
