package one.srp.gensmo.ui.components.collage

import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.IconButton
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.MoodboardEntity
import one.srp.gensmo.data.model.PostSearchItem
import one.srp.gensmo.data.model.SearchItem
import one.srp.gensmo.data.model.TryOnTaskItem
import one.srp.gensmo.data.remote.MoodboardService
import one.srp.gensmo.data.utils.JSON
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.env.EnvConf
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.mime.copyToClipboard
import one.srp.gensmo.utils.mime.downloadBitmapImage
import one.srp.gensmo.utils.mime.downloadUrlImage
import one.srp.gensmo.utils.mime.shareText

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SharePanelDrawer(
    open: Boolean = false,
    onClose: () -> Unit,
    content: @Composable () -> Unit,
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    if (open) {
        ModalBottomSheet(
            onDismissRequest = { onClose() },
            sheetState = sheetState,
            dragHandle = null,
        ) {
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center
                ) {
                    BottomSheetDefaults.DragHandle()
                }

                content()
            }
        }
    }
}

@Composable
fun CollageSharePanel(
    item: MoodboardEntity? = null,
    result: SearchItem? = null,
    refer: EventRefer? = EventRefer.Share,
) {
    val context = LocalContext.current
    val metricViewModel: MetricViewModel = hiltViewModel()

    val metric = refer?.let {
        metricViewModel.compatMetric(refer)
    } ?: { {} }

    LaunchedEffect(Unit) {
        metric(SelectItem(EventItemListName.Screen, method = EventMethod.PageView))
    }

    val shareLink = item?.id?.let { "${EnvConf.origin}/share/${it}" } ?: "Link is not available"

    val jsonContent = item?.parsedContent ?: remember(item) {
        if (item != null) JSON.decodeFromString<MoodboardContent>(item.content)
        else null
    }

    var previewBitmap by remember { mutableStateOf<ImageBitmap?>(null) }

    val coroutineScope = rememberCoroutineScope()
    val saveMoodboard = {
        coroutineScope.launch {
            result?.let { res ->
                item?.let { itm ->
                    val ori = JSON.encodeToString<SearchItem>(res.copy(moodboards = null))
                    val proc = JSON.decodeFromString<PostSearchItem>(ori)
                    val target = proc.copy(
                        moodboardId = itm.id,
                        isTryOn = itm.isTryOn,
                        moodboards = itm.copy(
                            content = itm.parsedContent?.let { JSON.encodeToString(itm.parsedContent) }
                                ?: run { itm.content },
                            parsedContent = null,
                        )
                    )

                    MoodboardService.api.postMoodboard(target)
                }
            }
        }
    }
    val onCopyLink = {
        metric(
            SelectItem(
                EventItemListName.ShareBtnCopy,
                method = EventMethod.Click,
                actionType = EventActionType.ExternalShare
            )
        )
        saveMoodboard()
        copyToClipboard(context, label = "share", text = shareLink)
    }
    val onShareLink = { method: String ->
        metric(
            SelectItem(
                when (method) {
                    "pinterest" -> EventItemListName.ShareBtnPinterest
                    "ins" -> EventItemListName.ShareBtnInstagram
                    "x" -> EventItemListName.ShareBtnX
                    "reddit" -> EventItemListName.ShareBtnReddit
                    "meta" -> EventItemListName.ShareBtnFacebook
                    else -> EventItemListName.ShareBtnSystem
                }, method = EventMethod.Click, actionType = EventActionType.ExternalShare
            )
        )
        saveMoodboard()
        shareTo(context, method, shareLink)
    }
    val onDownloadImage = {
        previewBitmap?.let {
            metric(
                SelectItem(
                    EventItemListName.ShareBtnDownload,
                    method = EventMethod.Click,
                    actionType = EventActionType.Download
                )
            )

            saveMoodboard()

            if (downloadBitmapImage(context, it.asAndroidBitmap())) {
                Toast.makeText(context, "Mood board saved.", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "Save failed.", Toast.LENGTH_SHORT).show()
            }
        } ?: run { }
    }

    SharePanel(
        link = shareLink,
        downloadable = previewBitmap != null,
        downloadTitle = stringResource(R.string.text_download_moodboard),
        onCopyLink = onCopyLink,
        onShareLink = onShareLink,
        onDownloadImage = onDownloadImage,
    ) {
        jsonContent?.let {
            Card(
                modifier = Modifier.heightIn(0.dp, 340.dp),
                border = BorderStroke(4.dp, Color.White),
                shape = RoundedCornerShape(12.dp),
            ) {
                MoodboardPreview(jsonContent, onPreviewGen = { previewBitmap = it })
            }
        }
    }
}

@Composable
fun TryOnSharePanel(item: TryOnTaskItem? = null, taskId: String? = null, imageUrl: String? = null) {
    val context = LocalContext.current

    val tryOnTaskId = taskId ?: item?.tryOnTaskId
    val tryOnUrl = imageUrl ?: item?.tryOnUrl

    val shareLink = tryOnTaskId?.let {
        "${EnvConf.origin}/try-on/share/${it}"
    } ?: "Link is not available"

    val onCopyLink = {
        copyToClipboard(context, label = "share", text = shareLink)
    }
    val onShareLink = { method: String ->
        shareTo(context, method, shareLink)
    }

    val coroutineScope = rememberCoroutineScope()
    val onDownloadImage = {
        coroutineScope.launch {
            if (downloadUrlImage(context, tryOnUrl)) {
                Toast.makeText(context, "Tryon look saved.", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "Save failed.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    SharePanel(
        link = shareLink,
        downloadable = true,
        downloadTitle = stringResource(R.string.text_download_tryon_look),
        onCopyLink = onCopyLink,
        onShareLink = onShareLink,
        onDownloadImage = { onDownloadImage() },
    ) {
        tryOnUrl?.let {
            Card(
                modifier = Modifier.heightIn(0.dp, 340.dp),
                border = BorderStroke(4.dp, Color.White),
                shape = RoundedCornerShape(12.dp),
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current).data(it).crossfade(true)
                        .build(),
                    contentDescription = null,
                    contentScale = ContentScale.FillHeight,
                    modifier = Modifier.fillMaxHeight()
                )
            }
        }
    }
}

@Composable
private fun SharePanel(
    link: String,
    downloadable: Boolean = false,
    downloadTitle: String = stringResource(R.string.text_download),
    onCopyLink: () -> Unit = {},
    onShareLink: (method: String) -> Unit = {},
    onDownloadImage: () -> Unit = {},
    content: @Composable () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    var displayLink by remember(link) { mutableStateOf(link) }

    fun copyLink() {
        onCopyLink()

        coroutineScope.launch {
            displayLink = "Link copied."
            delay(5000)
            displayLink = link
        }
    }

    Column(
        modifier = Modifier.padding(horizontal = 24.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(horizontal = 16.dp)
        ) {
            content()
        }

        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            Text("Copy and send link", fontSize = 14.sp)
            CopyCard(link = displayLink, onClick = { copyLink() })
        }

        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            Text("Other ways to share", fontSize = 14.sp)
            ShareCard(onClick = onShareLink)
        }

        Row(modifier = Modifier.padding(vertical = 16.dp)) {
            DownloadButton(downloadable, text = downloadTitle, onClick = onDownloadImage)
        }
    }
}

@Composable
private fun CopyCard(link: String, onClick: () -> Unit = {}) {
    Card(
        elevation = CardDefaults.cardElevation(0.dp),
        shape = RoundedCornerShape(4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent),
        modifier = Modifier
            .border(
                width = 1.dp, color = Color(0xFFB0B5B9), shape = RoundedCornerShape(4.dp)
            )
            .padding(0.5.dp)
            .height(48.dp)
            .background(color = Color.Transparent, shape = RoundedCornerShape(4.dp))
            .padding(horizontal = 8.dp),
    ) {
        Row(modifier = Modifier.padding(vertical = 8.dp)) {
            Box(
                modifier = Modifier.aspectRatio(1f), contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(R.drawable.icon_website),
                    contentDescription = "website",
                    modifier = Modifier.size(20.dp)
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxHeight()
                    .weight(1f),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = link,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .aspectRatio(1f),
                contentAlignment = Alignment.Center
            ) {
                Button(
                    onClick = onClick,
                    modifier = Modifier
                        .fillMaxHeight()
                        .aspectRatio(1f),
                    shape = RoundedCornerShape(0.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent
                    ),
                    contentPadding = PaddingValues(0.dp)
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_share_link),
                        contentDescription = "website",
                    )
                }
            }
        }
    }
}

@Composable
private fun ShareCard(onClick: (method: String) -> Unit = {}) {
    val shareMethods = listOf(
        "pinterest", "ins", "x", "reddit", "meta", "system"
    )

    Card(
        elevation = CardDefaults.cardElevation(0.dp),
        shape = RoundedCornerShape(4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent),
        modifier = Modifier
            .padding(0.5.dp)
            .height(48.dp)
            .background(color = Color.Transparent, shape = RoundedCornerShape(4.dp)),
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            shareMethods.forEach { method ->
                IconButton(
                    onClick = { onClick(method) }, modifier = Modifier.size(40.dp)
                ) {
                    Image(
                        painter = getShareIcon(method),
                        contentDescription = method,
                        modifier = Modifier.size(32.dp),
                    )
                }
            }
        }
    }
}

@Composable
private fun getShareIcon(method: String): Painter {
    return when (method) {
        "pinterest" -> painterResource(R.drawable.logo_pinterest)
        "ins" -> painterResource(R.drawable.logo_instagram)
        "x" -> painterResource(R.drawable.logo_x)
        "reddit" -> painterResource(R.drawable.logo_reddit)
        "meta" -> painterResource(R.drawable.logo_facebook)
        else -> painterResource(R.drawable.icon_share_default)
    }
}

@Composable
private fun DownloadButton(enabled: Boolean = true, text: String, onClick: () -> Unit = {}) {
    Button(
        modifier = Modifier
            .fillMaxWidth()
            .height(52.dp)
            .background(color = Color(0xFF222222), shape = RoundedCornerShape(size = 4.dp)),
        onClick = onClick,
        enabled = enabled,
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
            contentColor = Color.White,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = Color.Gray,
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            Text(text, style = AppThemeTextStyle.Body16H)
            Image(
                painter = painterResource(R.drawable.icon_image_file),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

private fun shareTo(context: Context, method: String = "system", link: String) {
    when (method) {
        "" -> {}
        else -> {
            shareText(context, "Share link", link)
        }
    }
}
