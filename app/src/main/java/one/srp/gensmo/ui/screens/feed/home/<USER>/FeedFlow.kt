package one.srp.gensmo.ui.screens.feed.home._components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.LoadState
import androidx.paging.compose.collectAsLazyPagingItems
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.remote.FeedTabType
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.ui.screens.feed._components.FeedCard
import one.srp.gensmo.ui.screens.feed._components.FeedList
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.viewmodel.feed.FeedTabsViewModel
import timber.log.Timber
import kotlin.math.max

@Composable
fun FeedFlow(
    modifier: Modifier = Modifier,
    viewModel: FeedTabsViewModel = hiltViewModel(),
    onItemClick: (FeedItem) -> Unit = {},
    onLikeClick: (FeedItem, Boolean) -> Unit = { _, _ -> },
    onScroll: () -> Unit = {},
    onLoadMore: (List<FeedItem>) -> Unit = {},
    onItemView: (FeedItem) -> Unit = {},
) {
    val itemList = viewModel.getPagingFlow(FeedTabType.Foryou).collectAsLazyPagingItems()

    LaunchedEffect(itemList.loadState) {
        val appendState = itemList.loadState.append
        if (appendState is LoadState.Loading) {
            val currentItems = itemList.itemSnapshotList.items
            onLoadMore(currentItems)
        }
    }

    FeedList(modifier = modifier, items = itemList, onScroll = { onScroll() }) {
        FeedFlowItem(
            item = it,
            onItemClick = onItemClick,
            onLikeClick = onLikeClick,
            onView = { onItemView(it) })
    }
}

@Composable
fun FeedFlowItem(
    item: FeedItem,
    onItemClick: (FeedItem) -> Unit,
    onLikeClick: (FeedItem, Boolean) -> Unit = { _, _ -> },
    onView: () -> Unit = {},
) {
    var liked by remember { mutableStateOf(item.isLiked) }

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.Home)

    val coroutineScope = rememberCoroutineScope()
    fun clickLike() {
        val newLike = !liked

        coroutineScope.launch {
            item.moodboardId.let { moodboardId ->
                liked = newLike
                item.isLiked = newLike
                item.likedCount = (item.likedCount ?: 0).let { count ->
                    if (newLike) count + 1 else max(count - 1, 0)
                }

                val eventItem = genEventItemByFeedType(item)
                metric(
                    SelectItem(
                        itemListName = EventItemListName.DualColumnFeedForYouListItemLikeBtn,
                        method = EventMethod.Click,
                        actionType = if (newLike) EventActionType.Like else EventActionType.CancelLike,
                        items = listOf(eventItem)
                    )
                )

                try {
                    val type =
                        if (item.type == "tryon") CollectionType.TryOn else CollectionType.Collage

                    if (newLike) {
                        UserService.api.likePost(moodboardId, type)
                    } else {
                        UserService.api.unlikePost(moodboardId, type)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    liked = !newLike
                    item.isLiked = !newLike
                    item.likedCount = (item.likedCount ?: 0).let { count ->
                        if (newLike) max(count - 1, 0) else count + 1
                    }
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        onView()
    }

    FeedCard(
        modifier = Modifier
            .padding(2.dp)
            .fillMaxWidth(),
        item = item,
        onItemClick = { onItemClick(item) },
        liked = liked,
        onLikeClick = { clickLike() }
    )
}

fun genEventItemByFeedType(item: FeedItem): EventItem {
    return (if (item.type == "tryon") EventItem(
        itemCategory = EventItemCategory.TryOnCollage,
        itemId = item.tryOnTaskId,
        itemName = item.reasoning
    )
    else EventItem(
        itemCategory = EventItemCategory.GeneralCollage,
        itemId = item.moodboardId,
        itemName = item.reasoning
    ))
}