package one.srp.gensmo.ui.screens.tryon.task

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.MoodboardTryOnItems
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchParams
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchQueryMessageWrapper
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.model.TryOnBackgroundParams
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.model.TryOnTaskTryOnItems
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.data.utils.JSON
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.collage.MoodboardTryOnPanel
import one.srp.gensmo.ui.components.collage.ProductRemixPanel
import one.srp.gensmo.ui.components.collage.RemixPanelDrawer
import one.srp.gensmo.ui.components.collage.SharePanelDrawer
import one.srp.gensmo.ui.components.collage.TryOnPanelDrawer
import one.srp.gensmo.ui.components.collage.TryOnSharePanel
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.BottomBar
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.product.panel.alternatives.ProductAlternativesDrawer
import one.srp.gensmo.ui.screens.tryon._viewmodel.TryOnGenerateViewModel
import one.srp.gensmo.ui.screens.tryon.task._components.BottomPanel
import one.srp.gensmo.ui.screens.tryon.task._components.PromptPanel
import one.srp.gensmo.ui.screens.tryon.task._components.TopBarAction
import one.srp.gensmo.ui.screens.tryon.task._components.TryOnContent
import one.srp.gensmo.ui.screens.tryon.task._viewmodel.TryOnBackgroundViewModel
import one.srp.gensmo.ui.screens.tryon.task._viewmodel.TryOnProductAlternativesViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.mime.downloadUrlImage
import timber.log.Timber
import java.util.UUID

@Composable
fun TryOnTaskScreen(
    navActions: NavActions = NavActions(),
    taskId: String? = null,
    viewModel: TryOnGenerateViewModel = hiltViewModel(),
    onUpdateRemixProduct: (ProductItem?) -> Unit = {},
    openTryOn: (TryOnParams) -> Unit = {},
    createSession: (SearchQueryMessage) -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    val result by viewModel.queryResult.collectAsState()
    val queryStatus by viewModel.queryStatus.collectAsState()
    val isTaskLoading by viewModel.isTaskLoading.collectAsState()
    val progress by viewModel.progress.collectAsState()
    val previewUrl by viewModel.previewUrl.collectAsState()
    val userModelId by viewModel.userModelId.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.loadUserModelId()

        if (result == null) {
            taskId?.let { viewModel.getTask(it) }
        }
    }

    val tryOnBgViewModel = hiltViewModel<TryOnBackgroundViewModel>()

    var searchPanelVisible by remember { mutableStateOf(false) }
    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf("") }

    val context = LocalContext.current
    val downloadTryOn = {
        coroutineScope.launch {
            if (downloadUrlImage(context, result?.tryOnUrl)) {
                Toast.makeText(context, "Look saved.", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "Save failed.", Toast.LENGTH_SHORT).show()
            }
        }
    }
    val gotoCloset = {
        navActions.navigateToCloset()
    }

    var shareOpen by remember { mutableStateOf(false) }
    val openShare = {
        shareOpen = true
    }

    val gotoTryOnHistory = {
        navActions.navigateToUserLibrary(1)
    }

    val remixProduct = { query: String?, imageUrl: String? ->
        navActions.navigateToCollageSearch(
            query = query ?: "", imageUrl = imageUrl
        )
    }

    val tryOnProduct = { product: ProductItem ->
        result?.let { item ->
            coroutineScope.launch {
                val model = UserDataStoreManager.getModelInfo()

                val params = if (!model.second.isNullOrEmpty()) {
                    TryOnParams(
                        modelId = model.second,
                        products = listOf(product) + (item.products?.filter { p -> p.tags?.cateTag != product.tags?.cateTag }
                            ?: emptyList()),
                        moodboardId = item.moodboardId,
                        internalImageList = product.mainImage?.link?.let { listOf(it) },
                    )
                } else {
                    TryOnParams(
                        products = listOf(product) + (item.products?.filter { p -> p.tags?.cateTag != product.tags?.cateTag }
                            ?: emptyList()),
                        moodboardId = item.moodboardId,
                        internalImageList = product.mainImage?.link?.let { listOf(it) },
                    )
                }
                openTryOn(params)
            }
        }
    }

    var saved by remember(result) { mutableStateOf(result?.isFavorited == true) }
    val saveTryOn = {
        coroutineScope.launch {
            result?.tryOnTaskId?.let {
                val newSaved = !saved
                saved = newSaved
                result?.isFavorited = newSaved

                try {
                    if (newSaved) {
                        UserService.api.postSaved(it, CollectionType.TryOn)
                    } else {
                        UserService.api.deleteSaved(it, CollectionType.TryOn)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    saved = !newSaved
                    result?.isFavorited = !newSaved
                }
            }
        }
    }

    var remixOpen by remember { mutableStateOf(false) }
    var remixText by remember { mutableStateOf("") }
    val commitRemix = { q: String? ->
        remixOpen = false
        result?.let { result ->
            tryOnBgViewModel.submitBgChange(
                TryOnBackgroundParams(
                    imageUrl = result.tryOnUrl ?: "",
                    prompt = q ?: result.outfitComment?.scenarioGuru?.backgroundPrompt ?: "",
                    negativePrompt = "",
                    tryOnTaskId = result.tryOnTaskId ?: "",
                    isAsync = true,
                )
            )

            Toast.makeText(context, "Generating", Toast.LENGTH_SHORT).show()
        }
    }

    val onSearch = { q: String, img: String, stylesList: String?, budget: String? ->
        if (q.trim().isNotEmpty()) {
            searchPanelVisible = false
            createSession(
                SearchQueryMessage(
                    sessionId = "default",
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = SearchQueryMessageWrapper(
                        searchQuery = SearchParams(
                            query = q,
                            imageUrl = img,
                            debugLevel = 0,
                            budget = budget ?: "",
                            isAsync = true,
                            route = "",
                            isPresetQuery = false,
                            moodboardVersion = "v2",
                            inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                                ?.filter { it.isNotEmpty() } ?: emptyList())
                        )
                    )
                )
            )
        }
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.surface,
        modifier = Modifier.background(MaterialTheme.colorScheme.surface),
        topBar = {
            TopBar(
                immersive = true,
                transparent = true,
                onBack = { navActions.back() },
                action = {
                    Box {
                        TopBarAction(
                            onChangeAvatar = { gotoCloset() },
                            onDownload = { downloadTryOn() },
                            onShare = { openShare() },
                            onViewTryOn = { gotoTryOnHistory() },
                        )

                        SharePanelDrawer(open = shareOpen, onClose = { shareOpen = !shareOpen }) {
                            TryOnSharePanel(result)
                        }
                    }
                })
        }, bottomBar = {
            BottomBar {
                if (result?.useDefaultModel == true) {
                    TryOnButton(
                        isTaskLoading = isTaskLoading,
                        progress = progress,
                        userModelId = userModelId,
                        previewUrl = previewUrl,
                        onNavigateToCreate = { navActions.navigateToTryOnCreate() },
                        onRegenerateTask = { viewModel.regenerateTask() },
                    )
                } else {
                    var tryOnProductOpen by remember { mutableStateOf(false) }
                    fun closeTryOnProduct() {
                        tryOnProductOpen = false
                    }

                    val tryOnProductResult = viewModel.tryOnProductResult.collectAsState().value

                    BottomPanel(
                        saved = saved,
                        showTryOnButton = result?.moodboardId != null,
                        onChangeBg = {
                            remixText = result?.outfitComment?.scenarioGuru?.backgroundPrompt ?: ""
                            remixOpen = true
                        },
                        onRemix = {
                            capturedSearchText =
                                result?.outfitComment?.searchStylist?.searchQuery ?: ""
                            searchPanelVisible = true
                        },
                        onTryOn = {
                            tryOnProductOpen = true
                        },
                        onSave = {
                            saveTryOn()
                        }
                    )

                    TryOnPanelDrawer(open = tryOnProductOpen, onClose = { closeTryOnProduct() }) {
                        tryOnProductResult?.let {
                            MoodboardTryOnPanel(
                                items = JSON.decodeFromString<MoodboardTryOnItems>(
                                    JSON.encodeToString(TryOnTaskTryOnItems.serializer(), it)
                                ),
                                onClose = { closeTryOnProduct() },
                                onTryOn = { selectedItems ->
                                    closeTryOnProduct()
                                    result?.let { item ->
                                        coroutineScope.launch {
                                            val model = UserDataStoreManager.getModelInfo()
                                            val modelId = model.second
                                            val selectedProducts =
                                                selectedItems.mapNotNull { selected ->
                                                    listOfNotNull(
                                                        it.box1,
                                                        it.box2
                                                    ).flatMap { boxItems ->
                                                        boxItems.flatMap { boxItem ->
                                                            listOfNotNull(
                                                                boxItem.top,
                                                                boxItem.bottom,
                                                                boxItem.fullBody
                                                            ).flatten()
                                                        }
                                                    }.find { p -> p.globalId == selected.globalId }
                                                }

                                            val imageType =
                                                selectedItems.filter { it.itemType == "user_image" }

                                            val params = if (!model.second.isNullOrEmpty()) {
                                                TryOnParams(
                                                    modelId = modelId,
                                                    moodboardId = item.moodboardId,
                                                    internalImageList = selectedItems.mapNotNull { p ->
                                                        p.imageUrl ?: p.mainImage?.link
                                                    },
                                                    products = selectedProducts,
                                                    userImage = imageType.firstOrNull()?.imageUrl,
                                                    userImageTag = imageType.firstOrNull()?.garmentType,
                                                )
                                            } else {
                                                TryOnParams(
                                                    moodboardId = item.moodboardId,
                                                    internalImageList = selectedItems.mapNotNull { p ->
                                                        p.imageUrl ?: p.mainImage?.link
                                                    },
                                                    products = selectedProducts,
                                                    userImage = imageType.firstOrNull()?.imageUrl,
                                                    userImageTag = imageType.firstOrNull()?.garmentType,
                                                )
                                            }
                                            openTryOn(params)
                                        }
                                    }
                                }
                            )
                        } ?: run {
                            BaseLoading(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .fillMaxHeight(0.5f)
                            )
                        }
                    }
                }
            }
        }) { paddingValues ->
        result?.let { result ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = paddingValues.calculateBottomPadding())
                    .statusBarsPadding()
            ) {
                Box {
                    TryOnContent(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(0.75f),
                        item = result,
                        viewModel = tryOnBgViewModel,
                    )

                    if (queryStatus == TaskStatus.Loading) {
                        BaseLoading(
                            modifier = Modifier
                                .background(Color.White.copy(0.4f))
                                .fillMaxWidth()
                                .aspectRatio(0.75f)
                        )
                    }


                    result.products?.let {
                        val listLen =
                            tryOnBgViewModel.bgStateMap.collectAsState().value.values.toList().size
                        ProductPanel(
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(
                                    horizontal = if (listLen > 0) 44.dp else 16.dp,
                                    vertical = if (listLen > 0) 52.dp else 16.dp
                                )
                                .zIndex(10f),
                            productList = it,
                            onRemix = remixProduct,
                            onTryOn = { tryOnProduct(it) },
                            moodboardId = result.moodboardId ?: "",
                            onUpdateRemixProduct = { onUpdateRemixProduct(it) },
                        )
                    }

                }

                val vScrollState = rememberScrollState()
                val bgColor = MaterialTheme.colorScheme.surface
                result.outfitComment?.let {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .verticalScroll(vScrollState)
                            .background(MaterialTheme.colorScheme.surface)
                            .drawWithCache {
                                val gradient = Brush.verticalGradient(
                                    colors = listOf(
                                        bgColor.copy(0.7f),
                                        Color.Transparent
                                    ),
                                    startY = 0f,
                                    endY = size.height / 1.5f
                                )
                                onDrawWithContent {
                                    drawContent()
                                    drawRect(brush = gradient)
                                }
                            }
                    ) {
                        PromptPanel(
                            item = it,
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                            onChangeBackground = { q ->
                                remixText = q ?: ""
                                remixOpen = true
                            },
                            onOpenSearchPanel = {
                                capturedSearchText = it ?: ""
                                searchPanelVisible = true
                            },
                            tryOnBackgroundViewModel = tryOnBgViewModel
                        )
                    }
                }
            }
        } ?: run {
            BaseLoading(modifier = Modifier.fillMaxSize())
        }
    }

    SearchPanel(
        isVisible = searchPanelVisible && !showCameraPicker,
        onDismiss = {
            searchPanelVisible = false
            capturedImageUri = null
            capturedSearchText = ""
        },
        viewModel = hiltViewModel(),
        imageUrl = capturedImageUri,
        searchQueryText = capturedSearchText,
        onCameraRequest = {
            showCameraPicker = true
        },
        onSearch = { q, img, stylesList, budget ->
            onSearch(q, img, stylesList, budget)
        },
        onUpdateQuery = { query ->
            capturedSearchText = query
        },
        navActions = navActions,
        placeholderText = stringResource(R.string.text_describe_an_occasion_vibe_or_something_you_want_help_with)
    )

    if (showCameraPicker) {
        CameraPicker(
            onPhotoTaken = { uri, searchQueryWords ->
                capturedImageUri = uri.toString()
                capturedSearchText += searchQueryWords
                showCameraPicker = false
            },
            onMiss = {
                showCameraPicker = false
                capturedImageUri = null
            }
        )
    }

    RemixPanelDrawer(open = remixOpen, onClose = { remixOpen = false }) {
        ProductRemixPanel(
            initQuery = remixText,
            onClose = { remixOpen = false },
            onCommit = { q, _ ->
                commitRemix(q)
            }
        )
    }
}

@Composable
private fun ProductPanel(
    modifier: Modifier = Modifier,
    moodboardId: String,
    productList: List<ProductItem>,
    onRemix: (String?, String?) -> Unit = { _, _ -> },
    onTryOn: (ProductItem) -> Unit = {},
    viewModel: TryOnProductAlternativesViewModel = hiltViewModel(),
    onUpdateRemixProduct: (ProductItem?) -> Unit = {},
) {
    var selectedProduct by remember { mutableStateOf<ProductItem?>(null) }
    fun closeProduct() {
        selectedProduct = null
    }

    val productAlternatives by viewModel.queryResult.collectAsState()
    LaunchedEffect(selectedProduct) {
        selectedProduct?.let {
            if (moodboardId.isNotEmpty()) {
                viewModel.getTryOnProductAlternatives(moodboardId, it.tags?.cateTag ?: "")
            }
        }
    }

    var remixOpen by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            productList.map { product ->
                Card(
                    modifier = Modifier
                        .size(60.dp)
                        .clickable { selectedProduct = product },
                    colors = CardDefaults.cardColors(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.onSurface
                    ),
                    shape = MaterialTheme.shapes.medium
                ) {
                    product.mainImage?.link?.let {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current).data(it)
                                .crossfade(true).build(),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize(),
                        )
                    }
                }
            }
        }

        ProductAlternativesDrawer(
            open = selectedProduct != null,
            onClose = { closeProduct() },
            selected = selectedProduct,
            products = productAlternatives,
            replaceable = false,
            onRemix = {
                closeProduct()
                onUpdateRemixProduct(it)
                onRemix("Complete the look", it.mainImage?.link)
            },
            onTryOn = {
                closeProduct()
                onTryOn(it)
            },
        )

        RemixPanelDrawer(open = remixOpen, onClose = { remixOpen = false }) {
            ProductRemixPanel(
                initQuery = "Style with this",
                initImageUrl = selectedProduct?.mainImage?.link,
                onClose = { remixOpen = false },
                onCommit = { q, i ->
                    remixOpen = false
                    onRemix(q, i)
                })
        }
    }
}

@Composable
private fun TryOnButton(
    isTaskLoading: Boolean,
    progress: AsyncTaskManager.Progress,
    userModelId: String?,
    previewUrl: String? = null,
    onNavigateToCreate: () -> Unit,
    onRegenerateTask: () -> Unit,
) {
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(EventRefer.TryOnGen)
    var hasTriggeredRegenerate by remember { mutableStateOf(false) }

    val buttonText = when {
        isTaskLoading -> "Ready in ${progress.total - progress.current}min..."
        userModelId.isNullOrEmpty() -> "Create your first Avatar  >"
        else -> "Try on with my avatar"
    }

    Button(
        onClick = {
            if (isTaskLoading || hasTriggeredRegenerate) return@Button
            if (userModelId.isNullOrEmpty()) {
                metric(
                    SelectItem(
                        itemListName = EventItemListName.TryOnBtn,
                        method = EventMethod.Click,
                        actionType = EventActionType.CreateReplica,
                    )
                )
                onNavigateToCreate()
            } else {
                hasTriggeredRegenerate = true
                onRegenerateTask()
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = MaterialTheme.shapes.extraSmall,
        enabled = !isTaskLoading && !hasTriggeredRegenerate
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (userModelId.isNullOrEmpty() || isTaskLoading) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_body_outline),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )
            } else {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(previewUrl)
                        .size(coil3.size.Size.ORIGINAL)
                        .build(),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.TopCenter,
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .graphicsLayer {
                            scaleX = 3f
                            scaleY = 3f
                            transformOrigin = TransformOrigin(0.5f, 0.0f)
                        }
                )
            }
            Text(
                buttonText,
                style = AppThemeTextStyle.Body16H
            )
        }
    }
}

