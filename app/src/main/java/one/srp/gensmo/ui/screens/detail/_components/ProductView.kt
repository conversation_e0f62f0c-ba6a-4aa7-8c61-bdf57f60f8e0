package one.srp.gensmo.ui.screens.detail._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricViewModel

@Composable
fun ProductView(
    refer: EventRefer = EventRefer.FeedDetail,
    renderTrigger: Int = 0,
    products: List<ProductItem>,
    onProductClick: (ProductItem) -> Unit = {},
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = stringResource(R.string.text_products_in_this_post),
            style = AppThemeTextStyle.Heading20D,
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(products) { item ->
                ProductViewItem(
                    refer = refer,
                    modifier = Modifier.width(140.dp),
                    renderTrigger = renderTrigger,
                    item = item,
                    onItemClick = onProductClick
                )
            }
        }
    }
}

@Composable
fun ProductViewItem(
    modifier: Modifier = Modifier,
    refer: EventRefer = EventRefer.FeedDetail,
    item: ProductItem,
    renderTrigger: Int,
    onItemClick: (ProductItem) -> Unit = {},
) {
    // Metric helper for this view, allows different refer
    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)
    LaunchedEffect(item.id) {
        metric(
            SelectItem(
                itemListName = EventItemListName.ProductListProduct,
                method = EventMethod.TrueViewTrigger,
                actionType = EventActionType.Default,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = item.id,
                        itemName = item.title ?: ""
                    )
                )
            )
        )
    }
    var saved by remember(item, renderTrigger) { mutableStateOf(item.isFavorited == true) }
    fun clickSave(newSaved: Boolean) {
        // 埋点：帖子详情页产品列表产品收藏按钮点击
        metric(
            SelectItem(
                itemListName = EventItemListName.ProductListProductSaveBtn,
                method = EventMethod.Click,
                actionType = if (newSaved) EventActionType.Save else EventActionType.Unsave,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = item.globalId,
                        itemName = item.title ?: ""
                    )
                )
            )
        )
    }

    // Wrap click to report product detail entry metric
    SaveActionContainer(
        type = CollectionType.Product,
        id = item.globalId,
        initialState = saved,
        onPreAction = {
            clickSave(it)
        },
        onAction = {
            item.isFavorited = it
        }) { state, onClick ->
        ProductCard(
            modifier = modifier,
            item = item,
            onItemClick = { product ->
                metric(
                    SelectItem(
                        itemListName = EventItemListName.ProductListProduct,
                        method = EventMethod.Click,
                        actionType = EventActionType.EnterProductDetail,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.Product,
                                itemId = product.globalId,
                                itemName = product.title ?: ""
                            )
                        )
                    )
                )
                onItemClick(product)
            },
            saved = state,
            onSave = { onClick() }
        )
    }
}
