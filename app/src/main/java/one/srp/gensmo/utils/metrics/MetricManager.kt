package one.srp.gensmo.utils.metrics

import com.google.firebase.Firebase
import com.google.firebase.analytics.analytics
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import one.srp.core.analytics.AnalyticsManager
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.events.MetricItem
import one.srp.core.analytics.providers.DebugAnalyticsProvider
import one.srp.core.analytics.providers.FirebaseAnalyticsProvider
import one.srp.core.analytics.providers.MetricApiAnalyticsProvider
import one.srp.gensmo.ApplicationScope
import one.srp.gensmo.BuildConfig
import one.srp.gensmo.utils.metrics.MetricData.withParams
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MetricManager @Inject constructor(
    @param:ApplicationScope private val appScope: CoroutineScope,
    private val analyticsManager: AnalyticsManager,
    private val metricApiAnalyticsProvider: MetricApiAnalyticsProvider,
) {
    fun init(): AnalyticsManager {
        if (BuildConfig.DEBUG) analyticsManager.registerProvider(DebugAnalyticsProvider())
        analyticsManager.registerProvider(metricApiAnalyticsProvider)
        analyticsManager.registerProvider(FirebaseAnalyticsProvider(Firebase.analytics))

        appScope.launch(Dispatchers.IO) {
            analyticsManager.initialize()
        }

        return analyticsManager
    }

    fun log(event: MetricEvent) {
        appScope.launch(Dispatchers.IO) {
            analyticsManager.logEvent(event)
        }
    }

    fun log(event: MetricItem) {
        appScope.launch(Dispatchers.IO) {
            val me = withParams(
                MetricEvent(
                    eventName = event.eventName,
                    refer = event.refer,
                    itemListName = event.itemListName,
                    items = event.items,
                    method = event.method,
                    actionType = event.actionType,
                )
            )
            analyticsManager.logEvent(me)
        }
    }
}
